version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: alwaad_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: alwaad_db
      POSTGRES_USER: alwaad_user
      POSTGRES_PASSWORD: alwaad_password_2024
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - alwaad_network

  redis:
    image: redis:7-alpine
    container_name: alwaad_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - alwaad_network

volumes:
  postgres_data:
  redis_data:

networks:
  alwaad_network:
    driver: bridge
