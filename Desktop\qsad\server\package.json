{"name": "alwaad-server", "version": "1.0.0", "description": "خادم نظام الوعد الصادق 3", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "node-cron": "^3.0.3", "winston": "^3.11.0", "compression": "^1.7.4", "rate-limiter-flexible": "^4.0.1", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pdf-lib": "^1.17.1", "xlsx": "^0.18.5", "qrcode": "^1.5.3", "sharp": "^0.33.1", "redis": "^4.6.11"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/node-cron": "^3.0.11", "@types/compression": "^1.7.5", "@types/node": "^20.10.4", "@types/qrcode": "^1.5.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "prisma": "^5.7.1"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}