// مخطط قاعدة البيانات - الوعد الصادق 3
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// جدول المستخدمين والصلاحيات
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String?  @unique
  password    String
  fullName    String
  phone       String?
  role        UserRole @default(SALES)
  isActive    Boolean  @default(true)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // العلاقات
  sales       Sale[]
  auditLogs   AuditLog[]
  
  @@map("users")
}

// أدوار المستخدمين
enum UserRole {
  ADMIN      // مدير عام
  MANAGER    // مدير
  SALES      // موظف مبيعات
  ACCOUNTANT // محاسب
}

// جدول العملاء
model Customer {
  id            String   @id @default(cuid())
  name          String
  phone         String   @unique
  email         String?
  address       String?
  nationalId    String?  @unique
  dateOfBirth   DateTime?
  customerType  CustomerType @default(INDIVIDUAL)
  creditLimit   Decimal  @default(0) @db.Decimal(10,2)
  currentDebt   Decimal  @default(0) @db.Decimal(10,2)
  totalPurchases Decimal @default(0) @db.Decimal(10,2)
  rating        Int      @default(5) // تقييم العميل من 1-5
  notes         String?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // العلاقات
  sales         Sale[]
  installments  Installment[]
  
  @@map("customers")
}

enum CustomerType {
  INDIVIDUAL // فردي
  COMPANY    // شركة
}

// جدول التصنيفات
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // العلاقات
  products    Product[]
  
  @@map("categories")
}

// جدول المنتجات
model Product {
  id              String   @id @default(cuid())
  name            String
  description     String?
  sku             String   @unique // رمز المنتج
  barcode         String?  @unique
  categoryId      String
  costPrice       Decimal  @db.Decimal(10,2)
  cashPrice       Decimal  @db.Decimal(10,2)
  installmentPrice Decimal @db.Decimal(10,2)
  quantity        Int      @default(0)
  minQuantity     Int      @default(5) // الحد الأدنى للتنبيه
  unit            String   @default("قطعة")
  weight          Decimal? @db.Decimal(8,3)
  dimensions      String?  // الأبعاد
  images          String[] // مصفوفة روابط الصور
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // العلاقات
  category        Category @relation(fields: [categoryId], references: [id])
  saleItems       SaleItem[]
  
  @@map("products")
}

// جدول المبيعات
model Sale {
  id              String      @id @default(cuid())
  invoiceNumber   String      @unique
  customerId      String
  userId          String
  saleDate        DateTime    @default(now())
  paymentType     PaymentType
  subtotal        Decimal     @db.Decimal(10,2)
  discount        Decimal     @default(0) @db.Decimal(10,2)
  tax             Decimal     @default(0) @db.Decimal(10,2)
  total           Decimal     @db.Decimal(10,2)
  paidAmount      Decimal     @default(0) @db.Decimal(10,2)
  remainingAmount Decimal     @default(0) @db.Decimal(10,2)
  status          SaleStatus  @default(PENDING)
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  // العلاقات
  customer        Customer    @relation(fields: [customerId], references: [id])
  user            User        @relation(fields: [userId], references: [id])
  items           SaleItem[]
  installmentPlan InstallmentPlan?
  
  @@map("sales")
}

enum PaymentType {
  CASH        // نقدي
  INSTALLMENT // تقسيط
  MIXED       // مختلط
}

enum SaleStatus {
  PENDING     // معلق
  COMPLETED   // مكتمل
  CANCELLED   // ملغي
  RETURNED    // مرتجع
}

// جدول عناصر المبيعات
model SaleItem {
  id          String  @id @default(cuid())
  saleId      String
  productId   String
  quantity    Int
  unitPrice   Decimal @db.Decimal(10,2)
  discount    Decimal @default(0) @db.Decimal(10,2)
  total       Decimal @db.Decimal(10,2)
  
  // العلاقات
  sale        Sale    @relation(fields: [saleId], references: [id], onDelete: Cascade)
  product     Product @relation(fields: [productId], references: [id])
  
  @@map("sale_items")
}

// جدول خطط التقسيط
model InstallmentPlan {
  id              String   @id @default(cuid())
  saleId          String   @unique
  totalAmount     Decimal  @db.Decimal(10,2)
  downPayment     Decimal  @default(0) @db.Decimal(10,2)
  installmentAmount Decimal @db.Decimal(10,2)
  numberOfInstallments Int
  interestRate    Decimal  @default(0) @db.Decimal(5,2)
  startDate       DateTime
  endDate         DateTime
  status          InstallmentPlanStatus @default(ACTIVE)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // العلاقات
  sale            Sale     @relation(fields: [saleId], references: [id])
  installments    Installment[]
  
  @@map("installment_plans")
}

enum InstallmentPlanStatus {
  ACTIVE    // نشط
  COMPLETED // مكتمل
  CANCELLED // ملغي
  OVERDUE   // متأخر
}

// جدول الأقساط
model Installment {
  id              String            @id @default(cuid())
  planId          String
  customerId      String
  installmentNumber Int
  amount          Decimal           @db.Decimal(10,2)
  dueDate         DateTime
  paidDate        DateTime?
  paidAmount      Decimal           @default(0) @db.Decimal(10,2)
  status          InstallmentStatus @default(PENDING)
  lateFee         Decimal           @default(0) @db.Decimal(10,2)
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  
  // العلاقات
  plan            InstallmentPlan   @relation(fields: [planId], references: [id])
  customer        Customer          @relation(fields: [customerId], references: [id])
  
  @@map("installments")
}

enum InstallmentStatus {
  PENDING   // معلق
  PAID      // مدفوع
  OVERDUE   // متأخر
  PARTIAL   // جزئي
}

// جدول سجل العمليات
model AuditLog {
  id          String   @id @default(cuid())
  userId      String
  action      String   // نوع العملية
  tableName   String   // اسم الجدول
  recordId    String   // معرف السجل
  oldValues   Json?    // القيم القديمة
  newValues   Json?    // القيم الجديدة
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  
  // العلاقات
  user        User     @relation(fields: [userId], references: [id])
  
  @@map("audit_logs")
}

// جدول الإعدادات
model Setting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  category    String   @default("general")
  isSystem    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("settings")
}

// جدول النسخ الاحتياطي
model Backup {
  id          String      @id @default(cuid())
  filename    String
  size        BigInt
  type        BackupType
  status      BackupStatus @default(IN_PROGRESS)
  startedAt   DateTime    @default(now())
  completedAt DateTime?
  error       String?
  
  @@map("backups")
}

enum BackupType {
  MANUAL    // يدوي
  AUTOMATIC // تلقائي
  SCHEDULED // مجدول
}

enum BackupStatus {
  IN_PROGRESS // قيد التنفيذ
  COMPLETED   // مكتمل
  FAILED      // فشل
}
