# الوعد الصادق 3 - نظام إدارة المبيعات والأقساط

## نظرة عامة
نظام شامل لإدارة المبيعات والأقساط مصمم خصيصاً للشركات العربية مع دعم كامل للغة العربية ونظام الأقساط المتقدم.

## المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع تشفير كلمات المرور
- ثلاثة مستويات صلاحيات: مدير، موظف مبيعات، محاسب
- تسجيل جميع العمليات (Audit Log)

### 📊 لوحة التحكم الذكية
- إحصائيات المبيعات اليومية والشهرية
- تتبع الأقساط المتأخرة
- تنبيهات المخزون المنخفض
- رسوم بيانية تفاعلية

### 👥 إدارة العملاء
- ملفات شاملة للعملاء
- تاريخ المشتريات والأقساط
- نظام تقييم العملاء
- إشعارات تلقائية

### 📦 إدارة المنتجات
- كتالوج منتجات مع الصور
- تتبع المخزون الفوري
- أسعار متعددة (نقدي/تقسيط)
- نظام الباركود

### 🧾 نظام الفواتير المتقدم
- فواتير نقدية وتقسيط
- حساب تلقائي للضرائب والخصومات
- طباعة احترافية PDF
- ربط مع الطابعات الحرارية

### 💰 نظام الأقساط الذكي
- جداول أقساط مرنة
- حساب الفوائد التلقائي
- تنبيهات الدفع
- تتبع المدفوعات

### 📈 التقارير الشاملة
- تقارير المبيعات والأرباح
- تحليل أداء المنتجات
- تقارير العملاء والأقساط
- تصدير Excel و PDF

## متطلبات النظام

### الحد الأدنى
- Windows 10 أو أحدث
- 4 GB RAM
- 2 GB مساحة تخزين
- اتصال إنترنت (للتحديثات)

### الموصى به
- Windows 11
- 8 GB RAM أو أكثر
- 5 GB مساحة تخزين
- SSD للأداء الأمثل

## التثبيت والتشغيل

### 1. متطلبات التطوير
```bash
# تثبيت Node.js (الإصدار 18 أو أحدث)
# تثبيت PostgreSQL
# تثبيت Git
```

### 2. إعداد المشروع
```bash
# استنساخ المشروع
git clone <repository-url>
cd alwaad-alsadiq-3

# تثبيت التبعيات
npm run setup

# إعداد قاعدة البيانات
docker-compose up -d
npm run db:setup
npm run db:seed
```

### 3. تشغيل التطبيق
```bash
# تشغيل وضع التطوير
npm run dev

# أو تشغيل الإنتاج
npm run build
npm start
```

### 4. الوصول للتطبيق
- الواجهة الأمامية: http://localhost:3000
- API الخلفي: http://localhost:5000
- قاعدة البيانات: localhost:5432

## بيانات الدخول الافتراضية

### المدير العام
- اسم المستخدم: admin
- كلمة المرور: admin123

### موظف المبيعات
- اسم المستخدم: sales
- كلمة المرور: sales123

### المحاسب
- اسم المستخدم: accountant
- كلمة المرور: acc123

## الهيكل التقني

### Frontend
- React 18 مع TypeScript
- Material-UI مع دعم RTL
- React Query للبيانات
- React Hook Form للنماذج
- Chart.js للرسوم البيانية

### Backend
- Node.js مع Express
- TypeScript للأمان
- Prisma ORM
- JWT للمصادقة
- bcrypt للتشفير

### قاعدة البيانات
- PostgreSQL 15
- Redis للتخزين المؤقت
- نسخ احتياطي تلقائي

## الأمان والحماية

### تشفير البيانات
- تشفير كلمات المرور بـ bcrypt
- JWT tokens آمنة
- HTTPS في الإنتاج
- تشفير البيانات الحساسة

### النسخ الاحتياطي
- نسخ تلقائي يومي
- تصدير البيانات
- استعادة سريعة
- تخزين آمن

## الدعم والصيانة

### التحديثات
- تحديثات أمنية تلقائية
- ميزات جديدة شهرياً
- إصلاح الأخطاء فورياً

### الدعم الفني
- دعم 24/7
- تدريب المستخدمين
- استشارات تقنية
- صيانة دورية

## الترخيص
هذا البرنامج مملوك لشركة الوعد الصادق ومحمي بحقوق الطبع والنشر.

## التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +964-XXX-XXXX
- الموقع: www.alwaad-alsadiq.com
