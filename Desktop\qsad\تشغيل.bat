@echo off
chcp 65001 >nul
title الوعد الصادق 3

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        الوعد الصادق 3                        ║
echo ║                  نظام إدارة المبيعات والأقساط                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل نظام الوعد الصادق 3...
echo.

echo 📋 معلومات التطبيق:
echo    الخادم: http://localhost:5000
echo    العميل: http://localhost:3000
echo.

echo 🔐 بيانات تسجيل الدخول:
echo    المدير العام: admin / admin123
echo    المبيعات: sales / sales123
echo    المحاسب: accountant / acc123
echo.

echo ⚠️  تأكد من تشغيل PostgreSQL قبل المتابعة
echo.

pause

echo 🔧 إصلاح التبعيات...
cd /d %~dp0client
echo    - تثبيت @tanstack/react-query-devtools...
npm install @tanstack/react-query-devtools >nul 2>&1
echo    - تثبيت @mui/x-data-grid...
npm install @mui/x-data-grid >nul 2>&1
echo ✅ تم إصلاح التبعيات!
echo.

cd /d %~dp0

echo 🔧 تشغيل الخادم...
start "الوعد الصادق - الخادم" cmd /k "cd /d %~dp0server && npm run dev"

echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul

echo 🎨 تشغيل العميل...
start "الوعد الصادق - العميل" cmd /k "cd /d %~dp0client && npm run dev"

echo.
echo ✅ تم تشغيل التطبيق بنجاح!
echo.
echo 🌐 سيتم فتح التطبيق في المتصفح تلقائياً...
timeout /t 3 /nobreak >nul

start http://localhost:3000

echo.
echo 📝 للإيقاف: أغلق نوافذ الأوامر أو اضغط Ctrl+C
echo.
pause
