{"name": "alwaad-alsadiq-3", "version": "1.0.0", "description": "نظام إدارة المبيعات والأقساط - الوعد الصادق 3", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "npm run client:build && npm run server:build", "client:build": "cd client && npm run build", "server:build": "cd server && npm run build", "start": "cd server && npm start", "setup": "npm install && cd server && npm install && cd ../client && npm install", "db:setup": "cd server && npx prisma generate && npx prisma db push", "db:seed": "cd server && npx prisma db seed"}, "keywords": ["sales", "installments", "management", "arabic", "pos"], "author": "الوعد الصادق", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}